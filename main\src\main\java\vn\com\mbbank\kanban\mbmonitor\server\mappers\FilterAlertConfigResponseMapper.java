package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.FilterAlertConfigResponse;

/**
 * Mapper interface for mapping between `FilterAlertConfigResponse` and `FilterAlertConfigEntity`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FilterAlertConfigResponseMapper extends
    KanbanBaseMapper<FilterAlertConfigResponse, FilterAlertConfigEntity> {
  FilterAlertConfigResponseMapper INSTANCE = Mappers.getMapper(FilterAlertConfigResponseMapper.class);

}

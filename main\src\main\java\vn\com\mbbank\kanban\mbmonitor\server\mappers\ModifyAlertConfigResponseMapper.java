package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigModifyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ModifyAlertConfigModifyResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ModifyAlertConfigResponse;

/**
 * Mapper interface for mapping between `ModifyAlertConfigResponse` and `ModifyAlertConfigEntity`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModifyAlertConfigResponseMapper extends
    KanbanBaseMapper<ModifyAlertConfigResponse, ModifyAlertConfigEntity> {
  ModifyAlertConfigResponseMapper INSTANCE = Mappers.getMapper(ModifyAlertConfigResponseMapper.class);


  /**
   * map from ModifyAlertConfigEntity to ModifyAlertConfigResponse.
   *
   * @param entity       ModifyAlertConfigEntity.
   * @param services     list of service
   * @param applications list of application
   * @param modifies     list of modify
   * @return ModifyAlertConfigResponse
   */
  default ModifyAlertConfigResponse map(ModifyAlertConfigEntity entity,
                                        List<ServiceEntity> services,
                                        List<ApplicationResponse> applications,
                                        List<ModifyAlertConfigModifyEntity> modifies) {

    var modifyAlertConfigResponse = this.map(entity);
    modifyAlertConfigResponse.setServices(ServiceResponseMapper.INSTANCE.map(services));
    modifyAlertConfigResponse.setApplications(applications);
    if (!CollectionUtils.isEmpty(modifies)) {
      List<ModifyAlertConfigModifyResponse> modifyResponses = new ArrayList<>();
      modifies.forEach(modify -> {
        ModifyAlertConfigModifyResponse modifyResponse = new ModifyAlertConfigModifyResponse();
        modifyResponse.setFieldName(modify.getFieldName());
        modifyResponse.setFieldValue(modify.getFieldValue());
        modifyResponse.setContentHtml(modify.getContentHtml());
        modifyResponses.add(modifyResponse);
      });
      modifyAlertConfigResponse.setModifies(modifyResponses);
    }
    return modifyAlertConfigResponse;
  }
}

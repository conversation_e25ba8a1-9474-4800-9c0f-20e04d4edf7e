package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants.FIELD_MAP;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.KafkaJobModel;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigExecutionMapEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaJobTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.KafkaTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.LogActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.ExecutionResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.JobKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.services.kafka.SysLogKafkaProducerService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupConverter;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AutoTriggerActionConfigRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TriggerExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ApplicationResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AutoTriggerActionConfigEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.AutoTriggerActionConfigResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AutoTriggerActionConfigRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.ApplicationService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigExecutionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ConditionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ServiceService;

/**
 * Service Logic AutoTriggerActionConfigServiceImpl.
 */
@Service
@RequiredArgsConstructor
public class AutoTriggerActionConfigServiceImpl extends BaseServiceImpl<AutoTriggerActionConfigEntity, String>
        implements AutoTriggerActionConfigService {

  private final AutoTriggerActionConfigRepository autoTriggerActionConfigRepository;
  private final AutoTriggerActionConfigEntityMapper autoTriggerActionConfigEntityMapper =
          AutoTriggerActionConfigEntityMapper.INSTANCE;
  private final ServiceService serviceService;
  private final ApplicationService applicationService;
  private final ExecutionService executionService;
  private final AutoTriggerActionConfigResponseMapper autoTriggerActionConfigResponseMapper =
          AutoTriggerActionConfigResponseMapper.INSTANCE;
  private final AutoTriggerActionConfigDependencyService dependencyService;
  private final AutoTriggerActionConfigExecutionMapService triggerExecutionMapService;
  private final SysLogKafkaProducerService sysLogKafkaProducerService;
  private final ConditionMapService conditionMapService;
  private final JobKafkaProducerService jobKafkaProducerService;

  @Override
  protected JpaCommonRepository<AutoTriggerActionConfigEntity, String> getRepository() {
    return autoTriggerActionConfigRepository;
  }

  public Page<AutoTriggerActionConfigResponse> findAllWithSearch(PaginationRequestDTO request)
          throws BusinessException {
    Page<AutoTriggerActionConfigResponse> configs = autoTriggerActionConfigRepository.findAllBySearch(request);
    if (configs.isEmpty()) {
      return configs;
    }
    List<String> configIds = configs.stream()
            .map(AutoTriggerActionConfigResponse::getId)
            .toList();
    List<TriggerExecutionResponse> mapping = triggerExecutionMapService.findExecutionResponseByConfigIdIn(configIds);

    Map<String, List<String>> configIdToExecutionIds = mapping.stream()
            .collect(Collectors.groupingBy(
                    TriggerExecutionResponse::getAutoTriggerActionConfigId,
                    Collectors.mapping(TriggerExecutionResponse::getExecutionId, Collectors.toList())
            ));

    Map<String, ExecutionEntity> idToExecution = executionService.findAllByIdIn(
            mapping.stream()
                    .map(TriggerExecutionResponse::getExecutionId)
                    .distinct()
                    .toList()
    ).stream().collect(Collectors.toMap(ExecutionEntity::getId, e -> e));

    configs.forEach(config -> {
      config.setRuleGroup(RuleGroupConverter.convertToRuleGroup(config.getRuleGroupColumn()));
      List<ExecutionResponse> executions = configIdToExecutionIds
              .getOrDefault(config.getId(), List.of()).stream()
              .map(idToExecution::get)
              .filter(Objects::nonNull)
              .map(ExecutionResponseMapper.INSTANCE::map)
              .toList();
      config.setExecutions(executions);
    });

    return configs;
  }

  @Override
  @Transactional(rollbackFor = BusinessException.class)
  public AutoTriggerActionConfigEntity save(AutoTriggerActionConfigRequest request) throws BusinessException {
    var isCreateMode = Objects.isNull(request.getId());
    validateTriggerConfigRequest(request);
    AutoTriggerActionConfigEntity autoTriggerActionConfigEntity;
    var formatName = StringUtils.capitalizeFirstLetter(request.getName());
    if (isCreateMode) {
      autoTriggerActionConfigEntity = autoTriggerActionConfigEntityMapper.map(request);
      autoTriggerActionConfigEntity.setActive(true);
      autoTriggerActionConfigEntity.setId(GeneratorUtil.generateId());
    } else {
      autoTriggerActionConfigEntity =
              autoTriggerActionConfigRepository.findById(request.getId()).orElseThrow(() -> new BusinessException(
                      ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND));
      dependencyService.deleteAllByAutoTriggerActionConfigId(request.getId());
      triggerExecutionMapService.deleteAllByAutoTriggerActionConfigId(request.getId());
      triggerExecutionMapService.deleteAllByAutoTriggerActionConfigId(request.getId());
      autoTriggerActionConfigEntityMapper.merge(autoTriggerActionConfigEntity, request);
    }
    List<String> serviceIds;
    List<String> applicationIds;
    if (AutoTriggerTypeEnum.CONDITION.equals(request.getTriggerType())) {
      autoTriggerActionConfigEntity.setCronExpression(null);
      serviceIds = request.getServiceIds();
      applicationIds = request.getApplicationIds();
    } else {
      autoTriggerActionConfigEntity.setRuleGroup(null);
      autoTriggerActionConfigEntity.setTimeSinceLastTrigger(null);
      serviceIds = new ArrayList<>();
      applicationIds = new ArrayList<>();
    }
    var services = serviceService.findAllByIdInAndDeleted(serviceIds, false);
    if (services.size() < serviceIds.size()) {
      throw new BusinessException(ErrorCode.SERVICE_IN_TRIGGER_BE_DELETED);
    }
    var executions = executionService.findAllByIdIn(request.getExecutionIds());
    if (executions.size() < request.getExecutionIds().size()) {
      throw new BusinessException(ErrorCode.EXECUTION_IN_TRIGGER_BE_DELETED);
    }

    List<ApplicationEntity> applications = new ArrayList<>();
    autoTriggerActionConfigEntity.setName(formatName);
    save(autoTriggerActionConfigEntity);
    if (!KanbanCommonUtil.isEmpty(services)) {
      applications = applicationService.findAllByIdInAndServiceIdInAndDeleted(
              applicationIds,
              services.stream().map(ServiceEntity::getId).toList(),
              false);
      if (applications.size() < applicationIds.size()) {
        throw new BusinessException(ErrorCode.AUTO_TRIGGER_APPLICATION_HAS_BE_DELETED);
      }
      var applicationMap = applications.stream().collect(Collectors.groupingBy(ApplicationEntity::getServiceId));
      var dependencyServices = new ArrayList<ServiceEntity>();
      var serviceWithAllApplications = new ArrayList<ServiceEntity>();
      for (ServiceEntity service : services) {
        if (applicationMap.containsKey(service.getId())) {
          dependencyServices.add(service);
        } else {
          serviceWithAllApplications.add(service);
        }
      }
      var newDependencies = new ArrayList<AutoTriggerActionConfigDependencyEntity>();
      dependencyServices.forEach(service -> {
        var dependency = new AutoTriggerActionConfigDependencyEntity();
        dependency.setAutoTriggerActionConfigId(autoTriggerActionConfigEntity.getId());
        dependency.setDependencyId(service.getId());
        dependency.setType(DependencyTypeEnum.SERVICE);
        newDependencies.add(dependency);
      });
      applications.forEach(application -> {
        var dependency = new AutoTriggerActionConfigDependencyEntity();
        dependency.setAutoTriggerActionConfigId(autoTriggerActionConfigEntity.getId());
        dependency.setDependencyId(application.getId());
        dependency.setType(DependencyTypeEnum.APPLICATION);
        newDependencies.add(dependency);
      });
      serviceWithAllApplications.forEach(service -> {
        var dependency = new AutoTriggerActionConfigDependencyEntity();
        dependency.setAutoTriggerActionConfigId(autoTriggerActionConfigEntity.getId());
        dependency.setDependencyId(service.getId());
        dependency.setType(DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION);
        newDependencies.add(dependency);
      });
      dependencyService.saveAll(newDependencies);
    }

    var newTriggerExecutionMappers = request.getExecutionIds().stream()
            .map(executionId -> {
              var entity = new AutoTriggerActionConfigExecutionMapEntity();
              entity.setAutoTriggerActionConfigId(autoTriggerActionConfigEntity.getId());
              entity.setExecutionId(executionId);
              return entity;
            })
            .collect(Collectors.toList());
    triggerExecutionMapService.saveAll(newTriggerExecutionMappers);

    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.AUTO_TRIGGER_ACTION,
            new KafkaJobModel<String>().type(
                    KafkaJobTypeEnum.NEW_OR_UPDATE).configId(autoTriggerActionConfigEntity.getId())
                    .cronTime(autoTriggerActionConfigEntity.getCronExpression()));
    sysLogKafkaProducerService.send(
            isCreateMode ? LogActionEnum.CREATE_AUTO_TRIGGER_EXECUTION : LogActionEnum.EDIT_AUTO_TRIGGER_EXECUTION,
            request.getName(),
            autoTriggerActionConfigResponseMapper.map(autoTriggerActionConfigEntity,
                    services,
                    ApplicationResponseMapper.INSTANCE.map(applications),
                    ExecutionResponseMapper.INSTANCE.map(executions),
                    conditionMapService.mapNameCondition(request.getRuleGroup(),
                            null,
                            true, FIELD_MAP)));
    return autoTriggerActionConfigEntity;
  }

  @Override
  public AutoTriggerActionConfigResponse findWithId(String id) throws BusinessException {
    var autoTriggerActionConfig = autoTriggerActionConfigRepository.findById(id)
            .orElseThrow(() -> new BusinessException(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND));
    var autoTriggerActionConfigDependencies = dependencyService.findAllByAutoTriggerActionConfigId(id);
    var triggerExecutionMappers = triggerExecutionMapService.findAllByAutoTriggerActionConfigId(id);

    var serviceIds = new ArrayList<String>();
    var applicationIds = new ArrayList<String>();
    for (AutoTriggerActionConfigDependencyEntity dependency : autoTriggerActionConfigDependencies) {
      if (DependencyTypeEnum.SERVICE.equals(dependency.getType())
              || DependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION.equals(dependency.getType())) {
        serviceIds.add(dependency.getDependencyId());
      }
      if (DependencyTypeEnum.APPLICATION.equals(dependency.getType())) {
        applicationIds.add(dependency.getDependencyId());
      }
    }
    List<ServiceEntity> services =
            CollectionUtils.isNotEmpty(serviceIds) ? serviceService.findAllByIdIn(serviceIds) : Collections.emptyList();
    List<ApplicationResponse> applications =
            CollectionUtils.isNotEmpty(applicationIds) ? applicationService.findAllByIdIn(applicationIds) :
                    Collections.emptyList();
    List<ExecutionEntity> executions = executionService.findAllById(triggerExecutionMappers.stream()
            .map(AutoTriggerActionConfigExecutionMapEntity::getExecutionId).collect(Collectors.toList()));
    return autoTriggerActionConfigResponseMapper.map(autoTriggerActionConfig, services,
            applications, ExecutionResponseMapper.INSTANCE.map(executions));
  }

  @Override
  public AutoTriggerActionConfigEntity updateActive(String id) throws BusinessException {
    var autoTriggerActionConfigEntity = autoTriggerActionConfigRepository.findById(id)
            .orElseThrow(() -> new BusinessException(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND));
    autoTriggerActionConfigEntity.setActive(!autoTriggerActionConfigEntity.getActive());
    autoTriggerActionConfigRepository.save(autoTriggerActionConfigEntity);
    sysLogKafkaProducerService.send(autoTriggerActionConfigEntity.getActive()
            ? LogActionEnum.ACTIVE_AUTO_TRIGGER_EXECUTION
            : LogActionEnum.INACTIVE_AUTO_TRIGGER_EXECUTION, autoTriggerActionConfigEntity.getName());
    return autoTriggerActionConfigEntity;
  }

  protected void validateTriggerConfigRequest(AutoTriggerActionConfigRequest request) throws BusinessException {
    String name = StringUtils.capitalizeFirstLetter(request.getName());
    var isUpdateMode = Objects.nonNull(request.getId());
    var isExist =
            isUpdateMode
                    ? autoTriggerActionConfigRepository.existsByIdNotAndNameIgnoreCase(request.getId(), name)
                    : autoTriggerActionConfigRepository.existsByNameIgnoreCase(name);
    if (isExist) {
      throw new BusinessException(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NAME_IS_EXISTED);
    }
  }

  @Override
  public List<String> findAllByCustomObjectId(Long id) {
    Set<String> tiggerConfigNamesSet = new HashSet<>();
    var configs = autoTriggerActionConfigRepository.findAll();
    for (AutoTriggerActionConfigEntity config : configs) {
      if (!KanbanCommonUtil.isEmpty(config.getRuleGroup()) && config.getRuleGroup().checkCustomObject(id)) {
        tiggerConfigNamesSet.add(config.getName());
      }
    }
    return new ArrayList<>(tiggerConfigNamesSet);
  }

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                       List<DependencyTypeEnum> type) {
    return autoTriggerActionConfigRepository.findDependencyNameByDependencyId(dependencyId, type);
  }

  @Transactional(rollbackFor = BusinessException.class)
  @Override
  public void deleteWithId(String id) throws BusinessException {
    var autoTriggerActionConfigEntity = autoTriggerActionConfigRepository.findById(id)
            .orElseThrow(() -> new BusinessException(ErrorCode.AUTO_TRIGGER_ACTION_CONFIG_NOT_FOUND));
    autoTriggerActionConfigRepository.deleteById(id);
    dependencyService.deleteAllByAutoTriggerActionConfigId(id);
    triggerExecutionMapService.deleteAllByAutoTriggerActionConfigId(id);
    jobKafkaProducerService.notifyJobUpdate(KafkaTypeEnum.AUTO_TRIGGER_ACTION,
            new KafkaJobModel<String>().type(
                    KafkaJobTypeEnum.DELETE).configId(id));
    sysLogKafkaProducerService.send(LogActionEnum.DELETE_AUTO_TRIGGER_EXECUTION,
            autoTriggerActionConfigEntity.getName());
  }

  @Override
  public List<String> findAllByPriorityConfigId(Long id) {
    Set<String> configNameSet = new HashSet<>();
    var configs = autoTriggerActionConfigRepository.findAll();
    for (AutoTriggerActionConfigEntity config : configs) {
      if (!KanbanCommonUtil.isEmpty(config.getRuleGroup()) && config.getRuleGroup().checkPriority(id)) {
        configNameSet.add(config.getName());
      }
    }
    return new ArrayList<>(configNameSet);
  }
}

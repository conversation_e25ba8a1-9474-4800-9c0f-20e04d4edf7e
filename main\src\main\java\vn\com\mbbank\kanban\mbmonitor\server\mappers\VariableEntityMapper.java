package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * VariableEntityMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VariableEntityMapper
    extends KanbanBaseMapper<VariableEntity, VariableRequest> {
  VariableEntityMapper INSTANCE = Mappers.getMapper(VariableEntityMapper.class);
}

package vn.com.mbbank.kanban.mbmonitor.server.dtos.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AutoTriggerTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.constants.CommonConstants;

/**
 * DTO for handling auto trigger action configuration requests.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AutoTriggerActionConfigRequest {
  String id;

  @NotBlank
  @Size(min = 1, message = "Auto trigger action config name can not be empty")
  @Size(
      max = CommonConstants.COMMON_NAME_MAX_LENGTH,
      message = "Auto trigger action config name has max {max} character"
  )
  String name;
  @Min(value = CommonConstants.MIN_TIME_SINCE_LAST_TRIGGER,
          message = "Time since last trigger must be >= {value} seconds")
  @Max(value = CommonConstants.MAX_TIME_SINCE_LAST_TRIGGER,
          message = "Time since last trigger must be <= {value} seconds")
  @NotNull
  Long timeSinceLastTrigger;

  @Size(
      max = CommonConstants.COMMON_DESCRIPTION_MAX_LENGTH,
      message = "Auto trigger action config name has max {max} character"
  )
  String description;

  RuleGroupType ruleGroup;

  @NotNull
  @Builder.Default
  List<String> serviceIds = new ArrayList<>();

  @Builder.Default
  List<String> applicationIds = new ArrayList<>();

  @NotNull
  @Builder.Default
  @Size(
          min = 1, message = "At least one execution is required"
  )
  List<String> executionIds = new ArrayList<>();

  String cronExpression;
  @Builder.Default
  AutoTriggerTypeEnum triggerType = AutoTriggerTypeEnum.CONDITION;
}

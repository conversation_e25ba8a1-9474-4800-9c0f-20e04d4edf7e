package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.drop.Drop;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterUtils;
import org.springframework.jdbc.core.namedparam.ParsedSql;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.core.services.common.BaseServiceImpl;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.mbmonitor.common.acl.AclPermissionModel;
import vn.com.mbbank.kanban.mbmonitor.common.antlr4.PythonParserUtils;
import vn.com.mbbank.kanban.mbmonitor.common.configs.RestTemplateConfig;
import vn.com.mbbank.kanban.mbmonitor.common.constants.CommonConstants;
import vn.com.mbbank.kanban.mbmonitor.common.constants.ExecutionConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ApiInfoRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SqlExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecuteScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionApiEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionParamEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ExternalExecutionUrl;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.ExecutionApiEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.ExecutionResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.mapper.execution.ExecutionScriptRequestMapper;
import vn.com.mbbank.kanban.mbmonitor.common.services.systems.CommonAclPermissionService;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ExceptionUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ExecutionApiParamUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionEntityMapper;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionRunningResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionGroupRepository;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionRepository;
import vn.com.mbbank.kanban.mbmonitor.server.services.AutoTriggerActionConfigExecutionMapService;
import vn.com.mbbank.kanban.mbmonitor.server.services.DatabaseConnectionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionApiService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionParamService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.SysPermissionService;
import vn.com.mbbank.kanban.mbmonitor.server.services.VariableService;

/**
 * Implementation of the ExecutionService providing functionality to manage and execute operations
 * with execution entities, handling execution groups, parameters, variables, and associated configurations.
 * This service is a core component for managing execution workflows and scripts in the system.
 * <p>
 * This class extends BaseServiceImpl and implements the ExecutionService interface,
 * inheriting common service methods and defining custom execution-specific business logic.
 * <p>
 * Key Responsibilities:
 * - CRUD operations for execution entities.
 * - Management of execution groups, parameters, and variables.
 * - Execution of scripts and SQL commands with validation.
 * - Interaction with database connections and configurations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExecutionServiceImpl extends BaseServiceImpl<ExecutionEntity, String> implements ExecutionService {
  private final ExecutionRepository executionRepository;
  private final ExecutionGroupRepository executionGroupRepository;
  private final ExecutionParamService executionParamService;
  private final VariableService variableService;
  private final DatabaseConnectionService databaseConnectionService;
  private final SysPermissionService sysPermissionService;
  private final CommonAclPermissionService commonAclPermissionService;
  private final AutoTriggerActionConfigExecutionMapService triggerExecutionMapService;
  private final Logger logger = LoggerFactory.getLogger(this.getClass());
  private final ExecutionEntityMapper executionEntityMapper = ExecutionEntityMapper.INSTANCE;
  private final ExecutionResponseMapper executionResponseMapper = ExecutionResponseMapper.INSTANCE;
  private final ExecutionRunningResponseMapper executionRunningResponseMapper = ExecutionRunningResponseMapper.INSTANCE;
  private final ExecutionScriptRequestMapper executionScriptRequestMapper = ExecutionScriptRequestMapper.INSTANCE;
  private final ExecutionApiEntityMapper executionApiEntityMapper = ExecutionApiEntityMapper.INSTANCE;
  private final ExecutionApiService executionApiService;

  @Autowired
  @Qualifier(RestTemplateConfig.REST_TEMPLATE_KEYCLOAK_CENTRALIZED)
  RestTemplate restTemplateKeycloakCentralized;
  @Value("${mbmonitor.execution.timeout}")
  private Long executeTimeOut;

  @Override
  protected JpaCommonRepository<ExecutionEntity, String> getRepository() {
    return executionRepository;
  }

  @Override
  public List<ExecutionResponse> findAllByExecutionGroupId(String executionGroupId) {
    List<ExecutionEntity> entities = executionRepository.findAllByExecutionGroupId(executionGroupId);
    List<String> executionIds = entities.stream()
        .map(ExecutionEntity::getId)
        .toList();
    List<ExecutionApiEntity> apiEntities = executionApiService.findAllByExecutionIdIn(executionIds);
    Map<String, ExecutionApiEntity> apiMap = apiEntities.stream()
        .collect(Collectors.toMap(ExecutionApiEntity::getExecutionId, Function.identity()));
    return entities.stream()
        .map(e -> ExecutionResponseMapper.INSTANCE.map(
            e,
            List.of(),
            List.of(),
            apiMap.get(e.getId())
        ))
        .toList();
  }

  @Override
  public Page<ExecutionResponse> findAll(PaginationRequestDTO page) {
    return executionRepository.findAll(page);
  }

  @Override
  @Transactional(rollbackFor = {BusinessException.class})
  public ExecutionEntity createOrUpdate(ExecutionRequest request) throws BusinessException {
    var id = request.getId();
    var isCreateMode = KanbanCommonUtil.isEmpty(id);
    validateExecutionRequest(request);
    ExecutionEntity execution;
    if (isCreateMode) {
      execution = executionEntityMapper.map(request);
      execution.setId(GeneratorUtil.generateId());
    } else {
      execution = executionRepository.findById(id)
              .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
      if (!Objects.equals(request.getExecutionGroupId(), execution.getExecutionGroupId())) {
        // update old permission
        sysPermissionService.updateModuleParentIdByModuleAndActionAndType(
                PermissionModuleEnum.RUN_EXECUTION,
                PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE,
                execution.getExecutionGroupId(), request.getExecutionGroupId());
      }
      executionEntityMapper.merge(execution, request);
      executionParamService.deleteAllByExecutionId(id);
    }
    execution.setName(StringUtils.capitalizeFirstLetter(request.getName().trim()));
    var res = executionRepository.save(execution);
    if (ExecutionTypeEnum.API.equals(request.getType())) {
      ApiInfoRequest apiInfo = request.getApiInfo();
      if (!Objects.nonNull(request.getApiInfo())
              || KanbanCommonUtil.isNullOrEmpty(apiInfo.getUrl())
              || !Objects.nonNull(apiInfo.getMethod())) {
        throw new BusinessException(ErrorCode.EXECUTION_API_INFO_INVALID);
      }

      String existingId = Optional.ofNullable(executionApiService.findApiInfoByExecutionId(execution.getId()))
              .map(ExecutionApiEntity::getId)
              .orElse(null);

      ExecutionApiEntity entity = new ExecutionApiEntity();
      entity.setExecutionId(execution.getId());
      entity.setId(existingId);
      entity = executionApiEntityMapper.merge(entity, apiInfo);
      executionApiService.save(entity);
    }
    processExecutionParams(res, request);
    return res;
  }

  @Override
  @Transactional(rollbackFor = {BusinessException.class})
  public void deleteWithId(String id) throws BusinessException {
    var execution = executionRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
    if (triggerExecutionMapService.existByExecutionId(id)) {
      throw new BusinessException(ErrorCode.EXECUTION_IS_EXISTING_IN_TRIGGER);
    }
    sysPermissionService.deleteAllByModuleAndActionAndTypeAndModuleParentIdAndModuleId(
        PermissionModuleEnum.RUN_EXECUTION,
        PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE,
        execution.getExecutionGroupId(), id);
    executionRepository.delete(execution);
    executionApiService.deleteApiInfoByExecutionId(id);
  }

  @Override
  public ExecutionResponse findWithId(String id) throws BusinessException {
    ExecutionEntity executionEntity = executionRepository.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
    List<ExecutionParamEntity> executionParamEntities =
        executionParamService.findAllByExecutionId(executionEntity.getId());
    Optional<ExecutionGroupEntity> executionGroupEntity =
        executionGroupRepository.findById(executionEntity.getExecutionGroupId());
    var response = executionResponseMapper.map(executionEntity, executionParamEntities, List.of(),
        executionApiService.findApiInfoByExecutionId(executionEntity.getId()));
    executionGroupEntity.ifPresent(groupEntity -> response.setExecutionGroupName(groupEntity.getName()));
    return response;
  }

  @Override
  public ExecutionResponse findByIdWithVariable(String id) throws BusinessException {
    var execution = executionRepository
        .findById(id).orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
    boolean isPermission = commonAclPermissionService.isAnyPermission(
        List.of(new AclPermissionModel(PermissionModuleEnum.RUN_EXECUTION,
            PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE, execution.getId(),
            execution.getExecutionGroupId())), true);
    if (!isPermission) {
      throw new BusinessException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }
    var params = executionParamService.findAllByExecutionId(id);
    if (ExecutionTypeEnum.PYTHON.equals(execution.getType()) || ExecutionTypeEnum.API.equals(execution.getType())) {
      var variables =
          variableService.findAllByNameIn(params.stream().map(ExecutionParamEntity::getName).toList());
      var executionApiInfo = executionApiService.findApiInfoByExecutionId(execution.getId());
      return executionResponseMapper.map(execution, params, variables, executionApiInfo);
    }
    return executionResponseMapper.map(execution, params, null, null);
  }

  @Override
  public ExecuteScriptResponse execute(ExecuteScriptRequest request) throws BusinessException {
    var execution = executionRepository.findById(request.getExecutionId())
            .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
    boolean hasPermission = commonAclPermissionService.isAnyPermission(
            List.of(new AclPermissionModel(PermissionModuleEnum.RUN_EXECUTION,
                    PermissionActionEnum.EXECUTE, PermissionTypeEnum.SUB_MODULE, execution.getId(),
                    execution.getExecutionGroupId())), true);
    if (!hasPermission) {
      throw new BusinessException(ErrorCode.USER_DONT_HAVE_PERMISSIONS);
    }
    var res = executionRunningResponseMapper.map(execution);
    var params = executionParamService.findAllByExecutionId(execution.getId());
    if (isExecutionInfoUpdated(request, execution, params)) {
      throw new BusinessException(ErrorCode.EXECUTION_HAS_BEEN_UPDATED);
    }
    request.setExecutionBy(getUserName());
    request.setCheckInfoUpdate(true);
    try {
      var result =
              restTemplateKeycloakCentralized.exchange(ExternalExecutionUrl.getUrl(ExternalExecutionUrl.EXECUTE_SCRIPT),
                      HttpMethod.POST, new HttpEntity<>(
                              executionScriptRequestMapper.map(execution, new ArrayList<>(), getUserName(), request)),
                      new ParameterizedTypeReference<ResponseData<ExecutionScriptResponse>>() {});
      var body = result.getBody();
      if (Objects.nonNull(body)) {
        var data = body.getData();
        res.setScriptResponse(data.getResult());
        res.setStatus(data.getStatus());
        res.setScriptError(data.getError());
        res.setSqlExecutionResponse(Objects.nonNull(data.getSqlExecutionResponse())
                ? SqlExecutionResponse.transformSqlData(data.getSqlExecutionResponse()) : null);
        res.setApiExecutionResponse(data.getApiExecutionResponse());
      } else {
        res.setStatus(ExecutionStatusEnum.COMPLETED);
      }
    } catch (HttpClientErrorException ex) {
      logger.error("Failed to run execution with id: {} error {}", request.getExecutionId(), ex.getMessage());
      ExceptionUtils.fromHttpClientError(ex);
    } catch (RestClientException exception) {
      Throwable cause = exception.getCause();
      if (cause instanceof SocketTimeoutException || cause instanceof ConnectException) {
        logger.error("Execution run too long with Id: {}", request.getExecutionId(), exception);
        throw new BusinessException(ErrorCode.EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED);
      }
      logger.error("Failed to execution python script with Id: {} error {}", request.getExecutionId(), exception);
      throw new BusinessException(ErrorCode.EXECUTION_RUN_FAILED);

    }
    return res;
  }

  @Override
  public boolean existsByDatabaseConnectionId(Long databaseConnectionId) {
    return executionRepository.existsByDatabaseConnectionId(databaseConnectionId);
  }

  @Override
  public List<ExecutionEntity> findAllByIdIn(List<String> ids) {
    return executionRepository.findAllByIdIn(ids);
  }

  @Override
  public List<ExecutionResponse> findAllByType(ExecutionTypeEnum type) {
    List<ExecutionEntity> entities = executionRepository.findAllByType(type);
    List<String> executionGroupIds = entities.stream()
            .map(ExecutionEntity::getExecutionGroupId)
            .toList();
    List<ExecutionGroupEntity> groupEntities = executionGroupRepository.findAllById(executionGroupIds);
    Map<String, String> groupIdToNameMap = groupEntities.stream()
            .collect(Collectors.toMap(ExecutionGroupEntity::getId, ExecutionGroupEntity::getName));
    return entities.stream()
            .map(e -> ExecutionResponseMapper.INSTANCE.map(
                    e,
                    List.of(),
                    List.of(),
                    null
            )).peek(response -> {
              String groupName = groupIdToNameMap.get(response.getExecutionGroupId());
              response.setExecutionGroupName(groupName);
            })
            .toList();
  }


  protected void validateExecutionRequest(ExecutionRequest request) throws BusinessException {
    var isUpdateMode = !KanbanCommonUtil.isEmpty(request.getId());

    // Not found execution
    if (isUpdateMode) {
      var execution = executionRepository.findById(request.getId())
          .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_NOT_FOUND));
      if (!Objects.equals(execution.getType(), request.getType())) {
        throw new BusinessException(ErrorCode.EXECUTION_TYPE_CAN_NOT_BE_UPDATE);
      }
    }

    var name = StringUtils.capitalizeFirstLetter(request.getName().trim());

    // Execution name existed
    var isNameExisted = isUpdateMode
        ? executionRepository.existsByIdNotAndNameIgnoreCaseAndExecutionGroupId(request.getId(),
        name, request.getExecutionGroupId()) :
        executionRepository.existsByNameIgnoreCaseAndExecutionGroupId(name,
            request.getExecutionGroupId());
    if (isNameExisted) {
      throw new BusinessException(ErrorCode.EXECUTION_NAME_IS_EXISTED);
    }
    executionGroupRepository.findById(request.getExecutionGroupId())
        .orElseThrow(() -> new BusinessException(ErrorCode.EXECUTION_GROUP_ID_CAN_NOT_BE_EMPTY));
    switch (request.getType()) {
      case SQL:
        if (Objects.isNull(request.getDatabaseConnectionId())) {
          throw new BusinessException(ErrorCode.EXECUTION_DATABASE_CONNECTION_ID_CAN_NOT_BE_EMPTY);
        }
        var dbConnection = databaseConnectionService.findById(request.getDatabaseConnectionId());
        if (Objects.isNull(dbConnection)) {
          throw new BusinessException(ErrorCode.EXECUTION_DATABASE_CONNECTION_NOT_FOUND);
        }
        if (!dbConnection.getIsActive()) {
          throw new BusinessException(ErrorCode.EXECUTION_DATABASE_CONNECTION_IS_INACTIVE);
        }
        if (isInvalidQuery(request.getScript())) {
          throw new BusinessException(ErrorCode.EXECUTION_SCRIPT_IS_INVALID);
        }
        break;
      case PYTHON:
        var analyzeResult = PythonParserUtils.analyzePythonScript(request.getScript());
        if (!analyzeResult.isValid()) {
          throw new BusinessException(ErrorCode.EXECUTION_SCRIPT_IS_INVALID);
        }
        var invalidModules =
            analyzeResult.getImports().stream().filter(
                module -> !ExecutionConstants.WHITE_LISTED_LIBS.contains(module)).toList();
        if (CollectionUtils.isNotEmpty(invalidModules)) {
          throw new BusinessException(ErrorCode.EXECUTION_PYTHON_SCRIPT_USE_DISALLOWED_MODULE,
              String.join(CommonConstants.DEFAULT_DELIMITER, invalidModules));
        }
        var invalidBuiltInFunction =
            analyzeResult.getBuiltInFunctions().stream()
                .filter(function -> !ExecutionConstants.WHITE_LISTED_FUNCTION.contains(function))
                .toList();
        if (CollectionUtils.isNotEmpty(invalidBuiltInFunction)) {
          throw new BusinessException(ErrorCode.EXECUTION_PYTHON_SCRIPT_USE_DISALLOWED_FUNCTION,
              String.join(CommonConstants.DEFAULT_DELIMITER, invalidBuiltInFunction));
        }
        break;
      case API:
        break;
      default:
        break;
    }
  }

  void processExecutionParams(ExecutionEntity execution, ExecutionRequest request) {
    var type = execution.getType();
    var envNames = switch (type) {
      case SQL -> {
        ParsedSql parsedSql = NamedParameterUtils.parseSqlStatement(execution.getScript());
        yield NamedParameterUtils.buildSqlParameterList(parsedSql, new MapSqlParameterSource()).stream()
            .map(SqlParameter::getName).toList();
      }
      case PYTHON -> PythonParserUtils.analyzePythonScript(execution.getScript()).getEnvironmentVariables();
      case API -> ExecutionApiParamUtils.extractTemplateParam(request.getApiInfo());
    };
    executionParamService.saveAll(envNames.stream().map(env -> {
      var param = new ExecutionParamEntity();
      param.setExecutionId(execution.getId());
      param.setName(env);
      return param;
    }).collect(Collectors.toList()));
  }

  boolean isInvalidQuery(String sql) {
    try {
      Statement statement = CCJSqlParserUtil.parse(sql);
      return statement instanceof Drop || statement instanceof Delete || statement instanceof Update;
    } catch (JSQLParserException e) {
      logger.error(e.getMessage(), e);
      return true;
    }
  }

  protected boolean isExecutionInfoUpdated(ExecuteScriptRequest scriptRequest, ExecutionEntity execution,
                                           List<ExecutionParamEntity> params) {
    return !Objects.equals(scriptRequest.getScript(), execution.getScript())
            || !Objects.equals(scriptRequest.getName(), execution.getName())
            || !Objects.equals(scriptRequest.getDescription(), execution.getDescription())
            || !Objects.equals(scriptRequest.getType(), execution.getType())
            || !Objects.equals(scriptRequest.getDatabaseConnectionId(), execution.getDatabaseConnectionId())
            || !Objects.equals(scriptRequest.getExecutionGroupId(), execution.getExecutionGroupId())
            || (!Objects.equals(params.size(), scriptRequest.getVariables().size()))
            || scriptRequest.getVariables().stream()
            .anyMatch(variable -> params.stream().noneMatch(param -> param.getName().equals(variable.getName())));
  }
}

package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ModifyAlertConfigRequest;

/**
 * Mapper interface for mapping between `ModifyAlertConfigEntity` and `ModifyAlertConfigRequest`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModifyAlertConfigEntityMapper extends
    KanbanBaseMapper<ModifyAlertConfigEntity, ModifyAlertConfigRequest> {
  ModifyAlertConfigEntityMapper INSTANCE = Mappers.getMapper(ModifyAlertConfigEntityMapper.class);

  /**
   * map from ModifyAlertConfigRequest to ModifyAlertConfigEntity.
   *
   * @param entity  ModifyAlertConfigEntity.
   * @param request ModifyAlertConfigRequest.
   */
  void merge(@MappingTarget ModifyAlertConfigEntity entity, ModifyAlertConfigRequest request);
}

package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorActionEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ActionModel;

/**
 * Mapper logic MonitorActionEntityMapper.
 *
 * <AUTHOR>
 * @created_date 29/5/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MonitorActionEntityMapper extends KanbanBaseMapper<MonitorActionEntity, ActionModel> {
  MonitorActionEntityMapper INSTANCE = Mappers.getMapper(MonitorActionEntityMapper.class);
}

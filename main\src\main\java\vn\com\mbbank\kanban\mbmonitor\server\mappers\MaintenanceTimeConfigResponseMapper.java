package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.FORMAT_YYYY_MM_DD_HH_MM_A;
import static vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils.convertDefaultStringDateToFormatedString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.core.utils.KanbanStringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ServiceResponse;

/**
 * Mapper logic MaintenanceTimeConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaintenanceTimeConfigResponseMapper extends
    KanbanBaseMapper<MaintenanceTimeConfigResponse, MaintenanceTimeConfigEntity> {
  MaintenanceTimeConfigResponseMapper INSTANCE = Mappers.getMapper(MaintenanceTimeConfigResponseMapper.class);


  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param entity       MaintenanceTimeConfigEntity.
   * @param services     list of service
   * @param applications list of application
   * @param dependencies dependencies of config
   * @param isMapDetail  is map response detail of config
   * @return MaintenanceTimeConfigResponse
   */
  default MaintenanceTimeConfigResponse map(MaintenanceTimeConfigEntity entity,
                                            List<ServiceEntity> services,
                                            List<ApplicationResponse> applications,
                                            List<MaintenanceTimeConfigDependencyEntity> dependencies,
                                            boolean isMapDetail) {
    var maintenanceTimeConfigResponse = this.map(entity);
    if (isMapDetail) {
      var dependencyMap = dependencies.stream()
          .collect(Collectors.toMap(MaintenanceTimeConfigDependencyEntity::getDependencyId, Function.identity()));
      var serviceDependencies = new ArrayList<ServiceResponse>();
      for (ServiceEntity service : services) {
        if (dependencyMap.containsKey(service.getId())) {
          var type = dependencyMap.get(service.getId()).getType();
          if (MaintenanceTimeConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION.equals(type)
              || MaintenanceTimeConfigDependencyTypeEnum.SERVICE.equals(type)) {
            serviceDependencies.add(ServiceResponseMapper.INSTANCE.map(service));
          }
        }
      }
      maintenanceTimeConfigResponse.setServices(serviceDependencies);
      maintenanceTimeConfigResponse.setApplications(
          applications.stream().filter(application -> dependencyMap.containsKey(application.getId())).toList());
    } else {
      maintenanceTimeConfigResponse.setDescription(
          KanbanStringUtils.isNullOrEmpty(entity.getDescription()) ? Strings.EMPTY : entity.getDescription());
      maintenanceTimeConfigResponse.setStatus(mapStatus(entity));
      maintenanceTimeConfigResponse.setValue(mapTimeValue(entity));
    }
    return maintenanceTimeConfigResponse;
  }

  private static String mapStatus(MaintenanceTimeConfigEntity config) {
    Date now = new Date();

    if (!Boolean.TRUE.equals(config.getActive())) {
      return MaintenanceTimeConfigStatusEnum.INACTIVE.getDescription();
    }
    if (MaintenanceTimeConfigTypeEnum.CRON_JOB.equals(config.getType())) {
      return MaintenanceTimeConfigStatusEnum.NEVER_EXPIRE.getDescription();
    }
    if ((MaintenanceTimeConfigTypeEnum.FROM_TIME_TO_TIME.equals(config.getType())
        || MaintenanceTimeConfigTypeEnum.NEXT_TIME.equals(config.getType()))
        && config.getStartTime() != null && config.getEndTime() != null) {
      Date startTime = config.getStartTime();
      Date endTime = config.getEndTime();
      if (!now.before(startTime) && !now.after(endTime)) {
        return MaintenanceTimeConfigStatusEnum.IN_PROGRESS.getDescription();
      }
      if (now.before(startTime)) {
        return MaintenanceTimeConfigStatusEnum.NEW.getDescription();
      }
    }
    return MaintenanceTimeConfigStatusEnum.EXPIRED.getDescription();
  }

  private static String mapTimeValue(MaintenanceTimeConfigEntity config) {
    if (config == null || config.getType() == null) {
      return Strings.EMPTY;
    }

    Date startTime = config.getStartTime();
    Date endTime = config.getEndTime();
    String unit = config.getUnit() != null ? config.getUnit().toString() : Strings.EMPTY;
    String cronExpression = config.getCronExpression() != null ? config.getCronExpression() : Strings.EMPTY;

    return switch (config.getType()) {
      case NEXT_TIME -> String.format("%d %s%s",
          config.getNextTime(),
          unit,
          Boolean.TRUE.equals(config.getActive()) && startTime != null && endTime != null
              ? String.format(" (From: %s - To: %s)",
              convertDefaultStringDateToFormatedString(startTime.toString(), FORMAT_YYYY_MM_DD_HH_MM_A),
              convertDefaultStringDateToFormatedString(endTime.toString(), FORMAT_YYYY_MM_DD_HH_MM_A))
              : Strings.EMPTY);
      case FROM_TIME_TO_TIME -> (startTime != null && endTime != null)
          ? String.format("From: %s - To: %s",
          convertDefaultStringDateToFormatedString(startTime.toString(), FORMAT_YYYY_MM_DD_HH_MM_A),
          convertDefaultStringDateToFormatedString(endTime.toString(), FORMAT_YYYY_MM_DD_HH_MM_A))
          : Strings.EMPTY;
      case CRON_JOB -> cronExpression;
    };
  }

}
